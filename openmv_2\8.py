import sensor, image, time
from pyb import millis, LED

sensor.reset()
sensor.set_hmirror(False)
sensor.set_vflip(False)
sensor.set_transpose(False)
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.VGA)
sensor.set_windowing([200,120,240,240])

clock = time.clock()
led = LED(3)

# 颜色阈值 - 更严格的阈值来减少误识别
black_threshold = (0, 45, -30, 30, -30, 30)  # 黑色阈值
white_threshold = (50, 110, -30, 30, -30, 30)  # 白色阈值

# 视野偏移量变量
view_offset = [0, 0]
err = [0, 0]
target_found = False
last_target_pos = [120, 120]

while True:
    timer = millis()
    clock.tick()
    img = sensor.snapshot()
    
    # 使用更严格的检测条件
    black_blobs = img.find_blobs([black_threshold], 
                                pixels_threshold=200,  # 增加像素阈值
                                area_threshold=1500,   # 增加面积阈值
                                merge=True)
    
    frame = None
    current_target_found = False
    
    if black_blobs:
        # 只选择最大的黑色区域
        largest_black = max(black_blobs, key=lambda b: b.pixels())
        
        area = largest_black.w() * largest_black.h()
        pixel_density = largest_black.pixels() / area if area > 0 else 0
        
        # 严格的密度检查
        if pixel_density < 0.3:
            white_blobs = img.find_blobs([white_threshold], 
                                       area_threshold=20,  # 增加白色区域最小面积
                                       roi=largest_black.rect(), 
                                       merge=False)
            
            if white_blobs:
                largest_white = max(white_blobs, key=lambda b: b.area())
                white_black_ratio = largest_white.pixels() / largest_black.pixels()
                
                # 更严格的比例和位置检查
                center_distance = abs(largest_white.cx() - largest_black.cx()) + abs(largest_white.cy() - largest_black.cy())
                
                if (1.5 < white_black_ratio < 6.0 and  # 更严格的比例范围
                    center_distance < 15 and           # 中心距离限制
                    largest_black.w() > 20 and         # 最小宽度
                    largest_black.h() > 20):           # 最小高度
                    
                    frame = largest_black
                    current_target_found = True
                    last_target_pos = [largest_white.cx(), largest_white.cy()]
    
    if frame and current_target_found:
        led.on()
        target_found = True
        
        # 使用白色区域中心作为目标点
        target_off = last_target_pos
        
        # 视野偏移算法 - 增加稳定性
        offset_factor = 0.3  # 降低偏移因子，减少抖动
        view_offset[0] += round((target_off[0] - 120) * offset_factor)
        view_offset[1] += round((target_off[1] - 120) * offset_factor)
        
        # 限制视野偏移范围
        view_offset[0] = min(200, max(0, view_offset[0]))
        view_offset[1] = min(120, max(0, view_offset[1]))
        
        err = [target_off[0] + view_offset[0] - 200, target_off[1] + view_offset[1] - 120]
        
        # 绘制检测结果
        img.draw_rectangle(frame.rect(), color=(255, 255, 255), thickness=2)
        img.draw_cross(target_off[0], target_off[1], color=(255, 0, 0), size=8)
        
    else:
        led.off()
        if target_found:
            # 目标丢失，逐渐重置视野偏移
            view_offset[0] = int(view_offset[0] * 0.95)
            view_offset[1] = int(view_offset[1] * 0.95)
        target_found = False
    
    # 绘制中心线和偏差指示
    img.draw_line(0, 120, 240, 120)  # 水平中心线
    img.draw_line(120, 0, 120, 240)  # 垂直中心线
    img.draw_circle(err[0]+120, err[1]+120, 5, fill=True, color=(255,0,0))
    
    # 视野跟踪
    sensor.set_windowing([200+view_offset[0], 120+view_offset[1], 240, 240])
    
    timer = millis() - timer
    print('用时', timer, '实时帧速', 1000/timer, '平均帧速', clock.fps())
