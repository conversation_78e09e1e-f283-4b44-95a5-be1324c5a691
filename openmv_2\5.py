import sensor, image, time
import math
from pyb import UART, LED

# 初始化串口（仅用于云台控制）
uart_gimbal = UART(3, 115200)
uart_gimbal.init(115200, bits=8, parity=None, stop=1)

# LED指示灯
led = LED(1)

# 初始化摄像头 - 仅保留基本设置
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.set_windowing((240, 240))
sensor.skip_frames(time = 100)

# 颜色阈值 - 借鉴第一个程序的阈值设置
black_threshold = (0, 35, -20, 20, -20, 20)    # 黑色范围（调整为更宽泛的阈值）
white_threshold = (60, 100, -20, 20, -20, 20)  # 白色区域阈值（新增）
laser_threshold = (200, 255, -128, 127, -128, 127)  # 激光点阈值

# 透视校正参数
CALIBRATION_DISTANCE = 30.0  # 标准矩形的真实尺寸(mm)
REFERENCE_PIXEL_SIZE = 100   # 在正视角度下矩形的像素尺寸

def advanced_perspective_correction(laser_x, laser_y, frame_rect, corners_data=None):
    """
    高级透视校正算法
    通过分析矩形的几何变形来计算真实的空间距离
    """
    x, y, w, h = frame_rect
    center_x = x + w // 2
    center_y = y + h // 2
    
    # 计算基础像素距离
    pixel_distance = math.sqrt((laser_x - center_x) ** 2 + (laser_y - center_y) ** 2)
    
    # 分析透视变形
    aspect_ratio = w / h if h > 0 else 1.0
    area = w * h
    
    # 计算透视缩放因子
    # 当矩形因透视而变形时，其面积和宽高比会改变
    expected_area = REFERENCE_PIXEL_SIZE ** 2
    area_scale = math.sqrt(area / expected_area) if expected_area > 0 else 1.0
    
    # 透视角度估算
    if aspect_ratio > 1.2:  # 水平拉伸
        tilt_angle = math.atan((aspect_ratio - 1.0) * 0.5)
        perspective_factor = 1.0 / math.cos(tilt_angle)
    elif aspect_ratio < 0.8:  # 垂直拉伸
        tilt_angle = math.atan((1.0 / aspect_ratio - 1.0) * 0.5)
        perspective_factor = 1.0 / math.cos(tilt_angle)
    else:
        perspective_factor = 1.0
    
    # 结合面积缩放和透视校正
    scale_factor = (CALIBRATION_DISTANCE / REFERENCE_PIXEL_SIZE) * area_scale * perspective_factor
    
    # 计算真实距离
    real_distance = pixel_distance * scale_factor
    
    return real_distance, perspective_factor, area_scale

def get_rectangle_quality_score(frame_rect):
    """
    评估矩形检测的质量，用于选择最佳的距离计算方法
    返回0-1之间的分数，1表示最佳质量
    """
    x, y, w, h = frame_rect
    
    # 检查宽高比（接近1.0更好）
    aspect_ratio = w / h if h > 0 else 0
    aspect_score = 1.0 - abs(aspect_ratio - 1.0)
    aspect_score = max(0, min(1, aspect_score))
    
    # 检查尺寸（适中的尺寸更好）
    area = w * h
    size_score = 1.0 if 5000 <= area <= 20000 else 0.5
    
    # 检查位置（靠近中心更好）
    center_x = x + w // 2
    center_y = y + h // 2
    distance_from_center = math.sqrt((center_x - CENTER_X) ** 2 + (center_y - CENTER_Y) ** 2)
    position_score = max(0, 1.0 - distance_from_center / 100.0)
    
    return (aspect_score * 0.5 + size_score * 0.3 + position_score * 0.2)

# PID控制器 - 加快响应速度
class PID:
    def __init__(self, P=0.2, I=0.0, D=0.0):
        self.Kp = P
        self.Ki = I
        self.Kd = D
        self.target = 0
        self.last_error = 0
        self.integral = 0
        self.integral_limit = 50  # 减小积分限幅提高响应速度

    def compute(self, current, dt):
        error = self.target - current
        # 积分项限幅
        self.integral = max(min(self.integral + error * dt, self.integral_limit),
                          -self.integral_limit)
        derivative = (error - self.last_error) / dt
        output = (self.Kp * error +
                 self.Ki * self.integral +
                 self.Kd * derivative)
        self.last_error = error
        return output

# 创建水平和垂直方向的PID控制器 - 加快响应参数
pid_x = PID(0.5, 0.0, 0.2)  # 增大P和D参数
pid_y = PID(0.5, 0.0, 0.2)

def find_improved_target(img, black_threshold, white_threshold):
    """
    改进的目标检测算法，结合两个程序的优势
    使用第一个程序的黑框内白色检测逻辑，但保留透视校正功能
    """
    # 首先找到所有黑色区域
    black_blobs = img.find_blobs([black_threshold], 
                                pixels_threshold=500,
                                area_threshold=5000,
                                merge=False)
    
    best_target = None
    best_score = 0
    
    if black_blobs:
        for black_blob in black_blobs:
            # 检查黑框的质量（借鉴第一个程序的逻辑）
            pixel_density = black_blob.pixels() / (black_blob.w() * black_blob.h())
            
            # 过滤掉完全黑色的区域（寻找黑框而不是黑色实心块）
            if pixel_density < 0.3:
                # 在黑框内寻找白色区域
                white_blobs = img.find_blobs([white_threshold],
                                           area_threshold=2,
                                           roi=black_blob.rect(),
                                           merge=False)
                
                if white_blobs:
                    largest_white = max(white_blobs, key=lambda b: b.area())
                    
                    # 验证白色区域和黑框的关系（第一个程序的验证逻辑）
                    white_black_ratio = largest_white.pixels() / black_blob.pixels()
                    center_distance = math.sqrt(
                        (largest_white.cx() - black_blob.cx()) ** 2 + 
                        (largest_white.cy() - black_blob.cy()) ** 2
                    )
                    
                    # 检查是否符合目标特征
                    if (2 < white_black_ratio < 4 and 
                        center_distance < 15):  # 稍微放宽距离要求
                        
                        # 计算目标质量分数
                        score = (4 - abs(white_black_ratio - 3)) * (15 - center_distance)
                        
                        if score > best_score:
                            best_score = score
                            best_target = {
                                'black_blob': black_blob,
                                'white_blob': largest_white,
                                'quality_score': score
                            }
    
    return best_target

def find_largest_blob(blobs):
    if not blobs:
        return None
    return max(blobs, key=lambda b: b.pixels())

def calculate_distance(x1, y1, x2, y2):
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

def calculate_perspective_corrected_distance(x1, y1, x2, y2, frame_rect):
    """
    在透视倾斜情况下计算校正后的距离
    通过分析矩形的变形来估算透视角度并校正距离
    """
    # 获取矩形的四个顶点
    fx, fy, fw, fh = frame_rect
    
    # 计算矩形的宽高比，用于评估透视变形
    aspect_ratio = fw / fh if fh > 0 else 1.0
    
    # 理想情况下矩形应该是正方形或已知比例
    expected_ratio = 1.0  # 假设目标矩形是正方形
    
    # 计算透视校正因子
    perspective_factor = 1.0
    if aspect_ratio > expected_ratio * 1.2:  # 水平拉伸，可能是水平倾斜
        perspective_factor = aspect_ratio / expected_ratio
    elif aspect_ratio < expected_ratio * 0.8:  # 垂直拉伸，可能是垂直倾斜
        perspective_factor = expected_ratio / aspect_ratio
    
    # 基础距离计算
    base_distance = math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)
    
    # 应用透视校正
    corrected_distance = base_distance / perspective_factor
    
    return corrected_distance

def calculate_distance_with_corners(laser_x, laser_y, corners):
    """
    利用矩形四个角点的信息计算更准确的距离
    通过分析角点间距来推断真实的比例关系
    """
    x, y, w, h = corners
    
    # 计算矩形中心
    center_x = x + w // 2
    center_y = y + h // 2
    
    # 计算矩形对角线长度（用于比例校正）
    diagonal_pixels = math.sqrt(w ** 2 + h ** 2)
    
    # 假设已知目标矩形的真实尺寸（需要根据实际情况调整）
    real_diagonal_size = 42.43  # 例如：30x30mm正方形的对角线长度
    
    # 计算每像素代表的真实距离
    pixel_to_real_ratio = real_diagonal_size / diagonal_pixels if diagonal_pixels > 0 else 1.0
    
    # 计算激光点到中心的像素距离
    pixel_distance = math.sqrt((laser_x - center_x) ** 2 + (laser_y - center_y) ** 2)
    
    # 转换为真实距离
    real_distance = pixel_distance * pixel_to_real_ratio
    
    return real_distance

def analyze_perspective_angle(frame_rect):
    """
    分析透视角度，返回倾斜信息
    """
    x, y, w, h = frame_rect
    
    # 计算宽高比
    aspect_ratio = w / h if h > 0 else 1.0
    
    # 分析倾斜类型
    if aspect_ratio > 1.3:
        return "horizontal_tilt", aspect_ratio
    elif aspect_ratio < 0.7:
        return "vertical_tilt", aspect_ratio
    else:
        return "normal", aspect_ratio

def calculate_rectangle_center(frame):
    """计算矩形对角线的交点（中心点）"""
    if frame is None:
        return None

    x, y, w, h = frame.rect()
    # 计算矩形中心点（对角线交点）
    center_x = x + w // 2
    center_y = y + h // 2

    return (center_x, center_y)

def calculate_perspective_corrected_center(frame):
    """
    在透视倾斜情况下计算校正后的真实中心点
    通过分析矩形的变形来估算真实的物理中心
    """
    if frame is None:
        return None
    
    x, y, w, h = frame.rect()
    
    # 分析透视变形
    aspect_ratio = w / h if h > 0 else 1.0
    
    # 基础几何中心
    geometric_center_x = x + w // 2
    geometric_center_y = y + h // 2
    
    # 透视校正偏移量
    offset_x = 0
    offset_y = 0
    
    # 水平透视校正
    if aspect_ratio > 1.2:  # 水平拉伸（水平倾斜）
        # 当矩形水平拉伸时，真实中心会向较窄的一侧偏移
        tilt_factor = (aspect_ratio - 1.0) * 0.3
        # 判断哪一侧更近（通常是较窄的一侧）
        # 假设左侧更近，中心向左偏移
        offset_x = -w * tilt_factor * 0.1
        
    elif aspect_ratio < 0.8:  # 垂直拉伸（垂直倾斜）
        # 当矩形垂直拉伸时，真实中心会向较窄的一侧偏移
        tilt_factor = (1.0 / aspect_ratio - 1.0) * 0.3
        # 假设上侧更近，中心向上偏移
        offset_y = -h * tilt_factor * 0.1
    
    # 应用偏移校正
    corrected_x = int(geometric_center_x + offset_x)
    corrected_y = int(geometric_center_y + offset_y)
    
    return (corrected_x, corrected_y)

def calculate_weighted_center(frame, method="geometric"):
    """
    使用不同方法计算中心点
    method: "geometric", "perspective", "weighted", "corner_analysis"
    """
    if frame is None:
        return None
    
    x, y, w, h = frame.rect()
    
    if method == "geometric":
        # 简单几何中心
        return (x + w // 2, y + h // 2)
    
    elif method == "perspective":
        # 透视校正中心
        return calculate_perspective_corrected_center(frame)
    
    elif method == "weighted":
        # 加权中心（考虑面积分布）
        # 通过分析矩形的密度分布来计算重心
        center_x = x + w // 2
        center_y = y + h // 2
        
        # 基于面积的权重调整
        aspect_ratio = w / h if h > 0 else 1.0
        if aspect_ratio > 1.3:  # 水平拉伸
            # 真实中心可能偏向较厚的部分
            weight_shift = (aspect_ratio - 1.0) * 0.2
            center_x += int(w * weight_shift * 0.1)
        elif aspect_ratio < 0.7:  # 垂直拉伸
            weight_shift = (1.0/aspect_ratio - 1.0) * 0.2
            center_y += int(h * weight_shift * 0.1)
        
        return (center_x, center_y)
    
    elif method == "corner_analysis":
        # 基于四个角点的分析来确定真实中心
        # 分析角点间的距离关系
        
        # 计算对角线长度
        diagonal1 = math.sqrt(w**2 + h**2)  # 主对角线
        
        # 如果矩形因透视而变形，对角线可能不等长
        # 真实中心应该是变形校正后的中心
        
        aspect_ratio = w / h if h > 0 else 1.0
        
        # 基于对角线分析的校正
        if abs(aspect_ratio - 1.0) > 0.3:  # 有明显变形
            # 估算透视角度并校正
            if aspect_ratio > 1.0:  # 水平拉伸
                correction_factor = 1.0 / math.sqrt(aspect_ratio)
                center_x = x + int(w * 0.5 * correction_factor)
                center_y = y + h // 2
            else:  # 垂直拉伸
                correction_factor = math.sqrt(aspect_ratio)
                center_x = x + w // 2
                center_y = y + int(h * 0.5 * correction_factor)
        else:
            center_x = x + w // 2
            center_y = y + h // 2
        
        return (center_x, center_y)
    
    else:
        # 默认返回几何中心
        return (x + w // 2, y + h // 2)

def select_best_center_method(frame):
    """
    根据矩形的变形程度自动选择最佳的中心计算方法
    """
    if frame is None:
        return None, "none"
    
    x, y, w, h = frame.rect()
    aspect_ratio = w / h if h > 0 else 1.0
    
    # 评估变形程度
    distortion_level = abs(aspect_ratio - 1.0)
    
    if distortion_level < 0.1:  # 变形很小
        method = "geometric"
    elif distortion_level < 0.3:  # 中等变形
        method = "weighted"
    elif distortion_level < 0.5:  # 较大变形
        method = "perspective"
    else:  # 严重变形
        method = "corner_analysis"
    
    center = calculate_weighted_center(frame, method)
    return center, method

def calculate_center_from_corners(img, frame_rect):
    """
    通过实际检测四个角点来计算真实中心
    这是最准确的方法，适用于严重透视变形的情况
    """
    x, y, w, h = frame_rect
    
    # 定义四个角点的预期位置
    corners = [
        (x, y),         # 左上
        (x + w, y),     # 右上
        (x, y + h),     # 左下
        (x + w, y + h)  # 右下
    ]
    
    # 在角点附近寻找更精确的边缘点
    refined_corners = []
    search_radius = 5
    
    for corner_x, corner_y in corners:
        # 在角点周围搜索边缘
        best_x, best_y = corner_x, corner_y
        max_gradient = 0
        
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                px = corner_x + dx
                py = corner_y + dy
                
                # 检查边界
                if 0 <= px < img.width() and 0 <= py < img.height():
                    # 计算此点的梯度（边缘强度）
                    if px > 0 and px < img.width()-1 and py > 0 and py < img.height()-1:
                        # 简单的梯度计算
                        gradient = abs(img.get_pixel(px+1, py)[0] - img.get_pixel(px-1, py)[0]) + \
                                 abs(img.get_pixel(px, py+1)[0] - img.get_pixel(px, py-1)[0])
                        
                        if gradient > max_gradient:
                            max_gradient = gradient
                            best_x, best_y = px, py
        
        refined_corners.append((best_x, best_y))
    
    # 使用精炼后的角点计算中心
    if len(refined_corners) == 4:
        # 计算对角线的交点
        # 对角线1: 左上到右下
        # 对角线2: 右上到左下
        
        # 使用线性方程求交点
        x1, y1 = refined_corners[0]  # 左上
        x2, y2 = refined_corners[3]  # 右下
        x3, y3 = refined_corners[1]  # 右上
        x4, y4 = refined_corners[2]  # 左下
        
        # 计算两条对角线的交点
        # 使用向量叉积方法
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        
        if abs(denom) > 0.001:  # 避免除零
            t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
            center_x = int(x1 + t * (x2 - x1))
            center_y = int(y1 + t * (y2 - y1))
            
            return (center_x, center_y), refined_corners
        else:
            # 如果对角线平行（不太可能），使用几何中心
            center_x = sum(corner[0] for corner in refined_corners) // 4
            center_y = sum(corner[1] for corner in refined_corners) // 4
            return (center_x, center_y), refined_corners
    
    # 如果角点检测失败，返回几何中心
    return (x + w // 2, y + h // 2), corners

# 发送云台控制命令 - 优化控制逻辑
def send_gimbal_command(x_speed, y_speed):
    # 速度限幅，增加最大速度
    x_speed = max(min(int(x_speed), 120), -120)  # 增加速度限制
    y_speed = max(min(int(y_speed), 120), -120)

    # 减小死区以提高响应速度
    if abs(x_speed) < 3:  # 减小死区
        x_speed = 0
    if abs(y_speed) < 3:
        y_speed = 0

    command = bytearray([0xFF, 0xFE,
                        x_speed & 0xFF,
                        y_speed & 0xFF,
                        (x_speed + y_speed) & 0xFF])
    uart_gimbal.write(command)

# 图像中心点
CENTER_X = 120
CENTER_Y = 120

# 添加自适应视野跟踪变量（借鉴第一个程序）
view_offset = [0, 0]  # 视野偏移补偿量
target_position = [CENTER_X, CENTER_Y]  # 当前目标位置

# 主循环
clock = time.clock()
target_found = False
last_send_time = 0
last_time = time.ticks_ms()
start_time = time.ticks_ms()  # 程序启动时间
target_time = None  # 记录找到目标的时间

while True:
    try:
        clock.tick()
        current_time = time.ticks_ms()
        dt = (current_time - last_time) / 1000.0
        last_time = current_time

        img = sensor.snapshot()

        # 使用改进的目标检测算法
        improved_target = find_improved_target(img, black_threshold, white_threshold)
        
        # 如果没有找到改进目标，回退到原始方法
        if not improved_target:
            black_blobs = img.find_blobs([black_threshold],
                                        pixels_threshold=500,
                                        area_threshold=5000,
                                        merge=True,
                                        margin=2,
                                        x_stride=2,
                                        y_stride=2)
            frame = find_largest_blob(black_blobs)
        else:
            frame = improved_target['black_blob']

        # 使用智能中心点计算方法
        center_pos = None
        center_method = "none"
        refined_corners = None
        
        if frame:
            # 获取矩形的变形程度
            x, y, w, h = frame.rect()
            aspect_ratio = w / h if h > 0 else 1.0
            distortion_level = abs(aspect_ratio - 1.0)
            
            # 对于严重变形的情况，使用高精度角点检测
            if distortion_level > 0.4:
                center_pos, refined_corners = calculate_center_from_corners(img, frame.rect())
                center_method = "corner_detection"
            else:
                # 自动选择最佳的中心计算方法
                center_pos, center_method = select_best_center_method(frame)

        if frame and center_pos:
            if not target_found:
                target_found = True
                target_time = (current_time - start_time) / 1000.0
                print("Target found at: %.3f seconds" % target_time)
                print("Target center coordinates: (%d, %d)" % (center_pos[0], center_pos[1]))

            # 更新目标位置用于视野跟踪
            target_position[0] = center_pos[0]
            target_position[1] = center_pos[1]
            
            # 添加自适应视野跟踪（借鉴第一个程序）
            view_offset[0] += round((target_position[0] - CENTER_X) * 0.3)
            view_offset[1] += round((target_position[1] - CENTER_Y) * 0.3)
            
            # 限制视野补偿范围
            view_offset[0] = max(-50, min(50, view_offset[0]))
            view_offset[1] = max(-50, min(50, view_offset[1]))

            # 回到矩形检测方式并优化绘制
            x, y, w, h = frame.rect()

            # 绘制矩形边框
            img.draw_rectangle(frame.rect(), color=(255, 255, 255), thickness=2)

            # 如果使用了改进的检测算法，也绘制白色区域
            if improved_target:
                white_blob = improved_target['white_blob']
                img.draw_rectangle(white_blob.rect(), color=(0, 255, 255), thickness=1)
                img.draw_cross(white_blob.cx(), white_blob.cy(), color=(0, 255, 255), size=4)

            # 绘制红色目标点（智能校正后的中心）- 只保留红色十字
            img.draw_cross(center_pos[0], center_pos[1], color=(255, 0, 0), size=8)

            # 计算目标位置相对于图像中心的偏移
            x_error = CENTER_X - center_pos[0]
            y_error = CENTER_Y - center_pos[1]

            # 控制发送频率
            if current_time - last_send_time > 10:  # 每10ms发送一次
                x_output = pid_x.compute(x_error, dt)
                y_output = pid_y.compute(y_error, dt)
                send_gimbal_command(x_output, y_output)
                last_send_time = current_time

                # 在黑框范围内寻找激光点，扩大搜索范围
                margin = 10  # 增加边距以提高激光点检测
                roi = (max(0, frame.rect()[0] - margin),
                      max(0, frame.rect()[1] - margin),
                      min(frame.rect()[2] + 2*margin, img.width()),
                      min(frame.rect()[3] + 2*margin, img.height()))

                laser_blobs = img.find_blobs([laser_threshold],
                                            roi=roi,
                                            pixels_threshold=1,  # 降低阈值提高检测
                                            area_threshold=1)
                laser = find_largest_blob(laser_blobs)

                if laser:
                    # 使用改进的距离计算方法
                    # 方法1：透视校正距离
                    corrected_distance = calculate_perspective_corrected_distance(
                        laser.cx(), laser.cy(), center_pos[0], center_pos[1], frame.rect())
                    
                    # 方法2：基于角点信息的距离（更准确）
                    corner_based_distance = calculate_distance_with_corners(
                        laser.cx(), laser.cy(), frame.rect())
                    
                    # 方法3：传统距离（保留用于对比）
                    traditional_distance = calculate_distance(laser.cx(), laser.cy(),
                                                            center_pos[0], center_pos[1])
                    
                    # 分析透视角度
                    tilt_type, ratio = analyze_perspective_angle(frame.rect())
                    
                    # 选择最适合的距离计算结果
                    if abs(ratio - 1.0) > 0.2:  # 有明显透视变形
                        final_distance = corner_based_distance
                        distance_method = "Corner"
                    else:
                        final_distance = corrected_distance
                        distance_method = "Corrected"

                    # 显示视觉反馈 - 只保留绿色十字
                    img.draw_cross(laser.cx(), laser.cy(), color=(0, 255, 0), size=6)

                    # 绘制连接线
                    img.draw_line(center_pos[0], center_pos[1],
                                laser.cx(), laser.cy(), color=(255, 255, 0))

                    # 显示状态信息、坐标和时间 - 精简版
                    status = "OK" if final_distance <= 2 else "ADJ"
                    img.draw_string(0, 0, "D=%.1fmm %s(%s)" % (final_distance, status, distance_method),
                                  scale=2, color=(255, 0, 0))
                    
                    # 显示目标检测方法
                    detection_method = "Improved" if improved_target else "Standard"
                    img.draw_string(0, 20, "Detection: %s" % detection_method,
                                  scale=1, color=(255, 255, 0))
                    
                    if target_time:
                        img.draw_string(0, 40, "Time: %.3fs" % target_time,
                                      scale=2, color=(255, 255, 0))
                    img.draw_string(0, 60, "Center:(%d,%d)" % (center_pos[0], center_pos[1]),
                                  scale=1, color=(255, 0, 0))
                    img.draw_string(0, 80, "Laser:(%d,%d)" % (laser.cx(), laser.cy()),
                                  scale=1, color=(0, 255, 0))

                    # 如果达到目标，记录完成时间
                    if final_distance <= 2:
                        completion_time = (current_time - start_time) / 1000.0
                        print("Target reached at: %.3f seconds" % completion_time)
                        led.on()
                    else:
                        led.off()
                else:
                    # 没有找到激光点时的显示
                    img.draw_string(0, 0, "Target found, searching laser...",
                                  scale=1, color=(255, 255, 0))
        else:
            if target_found:
                send_gimbal_command(0, 0)
                target_found = False
                target_time = None
            led.off()

        # 显示FPS和运行时间，添加性能监控
        current_runtime = (current_time - start_time) / 1000.0
        
        # 添加处理时间统计（借鉴第一个程序）
        processing_time = time.ticks_ms() - current_time
        if processing_time > 0:
            real_fps = 1000.0 / processing_time
        else:
            real_fps = clock.fps()
        
        # 在屏幕上显示性能信息
        img.draw_string(0, 200, "FPS:%.1f AvgFPS:%d" % (real_fps, clock.fps()),
                      scale=1, color=(0, 255, 0))
        img.draw_string(0, 210, "Time:%.1fs Process:%dms" % (current_runtime, processing_time),
                      scale=1, color=(0, 255, 0))
        
        # 显示视野偏移信息
        img.draw_string(0, 220, "ViewOffset:(%d,%d)" % (view_offset[0], view_offset[1]),
                      scale=1, color=(255, 255, 0))

    except Exception as e:
        print("Error:", e)
        time.sleep_ms(10)
        continue
