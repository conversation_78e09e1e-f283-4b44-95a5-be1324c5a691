import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA, Pin, PWM, TOUCH

# 激光瞄准系统 - K230平台
# 目标：识别A4纸黑色矩形边框，瞄准中心，2秒内发射激光，精度≤2cm

sensor = None

# PID控制器类
class PID:
    def __init__(self, P=0.5, I=0.0, D=0.1):
        self.Kp = P
        self.Ki = I
        self.Kd = D
        self.target = 0
        self.last_error = 0
        self.integral = 0
        self.integral_limit = 100

    def compute(self, current):
        error = self.target - current
        self.integral = max(min(self.integral + error, self.integral_limit), -self.integral_limit)
        derivative = error - self.last_error
        output = (self.Kp * error + self.Ki * self.integral + self.Kd * derivative)
        self.last_error = error
        return output

try:
    # 初始化摄像头 - 优化帧率
    sensor = Sensor(width=320, height=240)  # 进一步降低分辨率提高帧率
    sensor.reset()
    sensor.set_framesize(width=320, height=240)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.ST7701, to_ide=True, width=800, height=480)
    MediaManager.init()
    sensor.run()

    # 初始化触摸屏
    tp = TOUCH(0)

    # GPIO初始化
    fpioa = FPIOA()

    # 激光笔控制 - GPIO26
    try:
        fpioa.set_function(26, FPIOA.GPIO26)
        laser_pin = Pin(26, Pin.OUT)
        laser_pin.value(0)  # 初始关闭激光
        print("激光笔GPIO26配置成功")
    except Exception as e:
        print(f"激光笔配置失败: {e}")
        laser_pin = None

    # 步进电机控制引脚
    # X轴步进电机 - GPIO15, 16, 17, 19
    try:
        fpioa.set_function(15, FPIOA.GPIO15)
        fpioa.set_function(16, FPIOA.GPIO16)
        fpioa.set_function(17, FPIOA.GPIO17)
        fpioa.set_function(19, FPIOA.GPIO19)

        step_x1 = Pin(15, Pin.OUT)
        dir_x1 = Pin(16, Pin.OUT)
        step_x2 = Pin(17, Pin.OUT)
        dir_x2 = Pin(19, Pin.OUT)
        print("X轴步进电机GPIO配置成功")
    except Exception as e:
        print(f"X轴步进电机配置失败: {e}")
        step_x1 = step_x2 = dir_x1 = dir_x2 = None

    # Y轴步进电机 - GPIO27, 14, 61, 40
    try:
        fpioa.set_function(27, FPIOA.GPIO27)
        fpioa.set_function(14, FPIOA.GPIO14)
        fpioa.set_function(61, FPIOA.GPIO61)
        fpioa.set_function(40, FPIOA.GPIO40)

        step_y1 = Pin(27, Pin.OUT)
        dir_y1 = Pin(14, Pin.OUT)
        step_y2 = Pin(61, Pin.OUT)
        dir_y2 = Pin(40, Pin.OUT)
        print("Y轴步进电机GPIO配置成功")
    except Exception as e:
        print(f"Y轴步进电机配置失败: {e}")
        step_y1 = step_y2 = dir_y1 = dir_y2 = None

    # 系统状态 - 改为一次性瞄准发射
    system_state = "SEARCHING"  # SEARCHING, TARGET_FOUND, AIMING, FIRING, COMPLETED
    target_center = None
    start_time = ticks_ms()
    target_found_time = None
    mission_completed = False  # 任务完成标志

    # PID控制器
    pid_x = PID(P=0.8, I=0.1, D=0.2)
    pid_y = PID(P=0.8, I=0.1, D=0.2)

    # 图像处理参数
    image_center_x = 160  # 适应新的分辨率320x240
    image_center_y = 120

    # 动态阈值系统 - 自适应光线条件
    class AdaptiveThreshold:
        def __init__(self):
            self.black_base = (0, 60, -25, 25, -25, 25)  # 基础黑色阈值
            self.brightness_history = []
            self.current_threshold = self.black_base

        def update_threshold(self, img):
            """根据图像亮度动态调整阈值"""
            # 计算图像平均亮度
            gray_img = img.to_grayscale(copy=True)
            # 采样中心区域计算亮度
            roi_x, roi_y = img.width()//4, img.height()//4
            roi_w, roi_h = img.width()//2, img.height()//2

            brightness_sum = 0
            sample_count = 0
            for y in range(roi_y, roi_y + roi_h, 10):
                for x in range(roi_x, roi_x + roi_w, 10):
                    if x < img.width() and y < img.height():
                        brightness_sum += gray_img.get_pixel(x, y)
                        sample_count += 1

            if sample_count > 0:
                avg_brightness = brightness_sum / sample_count
                self.brightness_history.append(avg_brightness)
                if len(self.brightness_history) > 10:
                    self.brightness_history.pop(0)

                # 计算稳定的平均亮度
                stable_brightness = sum(self.brightness_history) / len(self.brightness_history)

                # 根据亮度调整阈值
                if stable_brightness < 80:  # 暗环境
                    self.current_threshold = (0, 45, -30, 30, -30, 30)
                elif stable_brightness < 120:  # 正常环境
                    self.current_threshold = (0, 60, -25, 25, -25, 25)
                elif stable_brightness < 160:  # 明亮环境
                    self.current_threshold = (0, 80, -20, 20, -20, 20)
                else:  # 强光环境
                    self.current_threshold = (0, 100, -15, 15, -15, 15)

                return self.current_threshold, stable_brightness

            return self.current_threshold, 128

    # 初始化自适应阈值
    adaptive_threshold = AdaptiveThreshold()

    # 触摸屏阈值调整功能
    manual_threshold_mode = False
    manual_threshold = [0, 60, -25, 25, -25, 25]  # 手动阈值
    touch_counter = 0
    threshold_adjust_mode = False

    def draw_threshold_controls(img):
        """绘制阈值调整控制界面"""
        if threshold_adjust_mode:
            # 绘制背景 - 适应320x240分辨率
            img.draw_rectangle(0, 0, 320, 120, color=(0, 0, 0), thickness=1, fill=True)

            # 绘制控制按钮 - 重新设计布局
            button_color = (100, 100, 100)
            text_color = (255, 255, 255)

            # 第一行按钮
            # 自动/手动切换按钮
            mode_text = "自动" if not manual_threshold_mode else "手动"
            img.draw_rectangle(5, 5, 60, 25, color=button_color, thickness=1, fill=True)
            img.draw_string_advanced(10, 10, 16, mode_text, color=text_color)

            # 退出按钮
            img.draw_rectangle(250, 5, 60, 25, color=(200, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(270, 10, 16, "退出", color=text_color)

            if manual_threshold_mode:
                # 第二行：灰度阈值调整按钮
                img.draw_rectangle(5, 35, 40, 25, color=button_color, thickness=1, fill=True)
                img.draw_string_advanced(10, 40, 14, "更暗", color=text_color)

                img.draw_rectangle(50, 35, 40, 25, color=button_color, thickness=1, fill=True)
                img.draw_string_advanced(55, 40, 14, "更亮", color=text_color)

                # 预设按钮
                img.draw_rectangle(95, 35, 50, 25, color=button_color, thickness=1, fill=True)
                img.draw_string_advanced(105, 40, 14, "暗环境", color=text_color)

                img.draw_rectangle(150, 35, 50, 25, color=button_color, thickness=1, fill=True)
                img.draw_string_advanced(160, 40, 14, "亮环境", color=text_color)

                # 显示当前阈值详细信息
                img.draw_string_advanced(5, 70, 16, f"灰度阈值: {manual_threshold[1]}", color=(255, 255, 0))
                img.draw_string_advanced(5, 85, 12, f"完整: {manual_threshold}", color=(200, 200, 200))

            # 使用说明
            img.draw_string_advanced(5, 95, 14, "长按1.5秒进入/退出阈值调整", color=(255, 255, 0))

    def handle_threshold_touch(x, y):
        """处理阈值调整触摸事件 - 适应320x240分辨率"""
        global manual_threshold_mode, manual_threshold, threshold_adjust_mode

        if not threshold_adjust_mode:
            return

        # 第一行按钮
        # 模式切换按钮
        if 5 <= x <= 65 and 5 <= y <= 30:
            manual_threshold_mode = not manual_threshold_mode
            print(f"切换到{'手动' if manual_threshold_mode else '自动'}阈值模式")

        # 退出按钮
        elif 250 <= x <= 310 and 5 <= y <= 30:
            threshold_adjust_mode = False
            print("退出阈值调整模式")

        # 手动模式下的第二行按钮
        elif manual_threshold_mode:
            if 5 <= x <= 45 and 35 <= y <= 60:  # 更暗（降低灰度上限）
                manual_threshold[1] = max(10, manual_threshold[1] - 10)
                print(f"灰度阈值降低（检测更暗的黑色）: {manual_threshold[1]}")
            elif 50 <= x <= 90 and 35 <= y <= 60:  # 更亮（提高灰度上限）
                manual_threshold[1] = min(200, manual_threshold[1] + 10)
                print(f"灰度阈值提高（检测更亮的黑色）: {manual_threshold[1]}")
            elif 95 <= x <= 145 and 35 <= y <= 60:  # 暗环境
                manual_threshold = [0, 45, -30, 30, -30, 30]
                print("设置为暗环境阈值: 检测更深的黑色")
            elif 150 <= x <= 200 and 35 <= y <= 60:  # 亮环境
                manual_threshold = [0, 100, -15, 15, -15, 15]
                print("设置为亮环境阈值: 检测较浅的黑色")

    print("激光瞄准系统启动...")
    print("触摸屏操作:")
    print("- 长按屏幕1.5秒进入阈值调整模式")
    print("- 在调整模式下可以切换自动/手动阈值")
    print("- 手动模式下可以调整阈值参数")
    print("")
    print("阈值说明:")
    print("- 使用LAB颜色空间: (L_min, L_max, A_min, A_max, B_min, B_max)")
    print("- L通道(亮度): 0=黑色, 100=白色")
    print("- A通道(绿红): -128=绿色, +127=红色")
    print("- B通道(蓝黄): -128=蓝色, +127=黄色")
    print("- 黑色矩形主要调整L通道的最大值")
    print("- 更暗=降低L_max, 更亮=提高L_max")

    # 步进电机控制函数 - 使用GPIO脉冲控制
    def control_stepper_x(direction, steps, speed=1):
        """控制X轴步进电机 - GPIO脉冲控制"""
        if steps <= 0 or not all([step_x1, step_x2, dir_x1, dir_x2]):
            return

        # 设置方向
        dir_x1.value(1 if direction > 0 else 0)
        dir_x2.value(0 if direction > 0 else 1)

        # 优化延时提高速度
        if steps > 20:
            delay = max(1, 3 - speed)  # 快速移动
        elif steps > 5:
            delay = max(1, 3 - speed)  # 中速移动
        else:
            delay = 2  # 精确移动

        # 发送步进脉冲
        for _ in range(abs(steps)):
            step_x1.value(1)
            step_x2.value(1)
            time.sleep_ms(delay)
            step_x1.value(0)
            step_x2.value(0)
            time.sleep_ms(delay)

    def control_stepper_y(direction, steps, speed=1):
        """控制Y轴步进电机 - GPIO脉冲控制"""
        if steps <= 0 or not all([step_y1, step_y2, dir_y1, dir_y2]):
            return

        # 设置方向
        dir_y1.value(1 if direction > 0 else 0)
        dir_y2.value(0 if direction > 0 else 1)

        # 优化延时提高速度
        if steps > 20:
            delay = max(1, 3 - speed)  # 快速移动
        elif steps > 5:
            delay = max(1, 3 - speed)  # 中速移动
        else:
            delay = 2  # 精确移动

        # 发送步进脉冲
        for _ in range(abs(steps)):
            step_y1.value(1)
            step_y2.value(1)
            time.sleep_ms(delay)
            step_y1.value(0)
            step_y2.value(0)
            time.sleep_ms(delay)

    def find_target_rectangle(img):
        """寻找目标矩形 - 自适应/手动阈值识别黑色边框"""
        # 选择阈值模式
        if manual_threshold_mode:
            current_threshold = tuple(manual_threshold)
            brightness = 128  # 手动模式不计算亮度
        else:
            current_threshold, brightness = adaptive_threshold.update_threshold(img)

        # 多尺度检测，适应不同距离
        best_target = None
        best_score = 0

        # 恢复更好的检测参数，保证识别效果
        detection_params = [
            # (pixels_threshold, area_threshold, merge) - 从近到远
            (600, 3000, True),   # 近距离，大目标
            (300, 1500, True),   # 中距离
            (150, 600, True),    # 远距离，小目标
            (80, 300, False),    # 极远距离，不合并
        ]

        for pixels_thresh, area_thresh, merge in detection_params:
            black_blobs = img.find_blobs([current_threshold],
                                       pixels_threshold=pixels_thresh,
                                       area_threshold=area_thresh,
                                       merge=merge)

            for blob in black_blobs:
                # 检查矩形特征
                w, h = blob.w(), blob.h()
                aspect_ratio = w / h if h > 0 else 0
                area = w * h
                pixel_density = blob.pixels() / area if area > 0 else 0

                # 评估目标质量 - 针对320x240分辨率优化

                # 1. 面积评分 - 适应小分辨率
                if area >= 2000:  # 近距离大目标
                    area_score = 1.0
                elif area >= 1000:  # 中距离
                    area_score = 0.9
                elif area >= 500:  # 远距离
                    area_score = 0.8
                elif area >= 200:   # 极远距离
                    area_score = 0.6
                else:
                    area_score = 0.2

                # 2. 宽高比评分 - 考虑透视变形
                if 0.3 <= aspect_ratio <= 3.0:  # 允许更大的变形范围
                    if 0.7 <= aspect_ratio <= 1.4:  # 接近正方形
                        ratio_score = 1.0
                    elif 0.5 <= aspect_ratio <= 2.0:  # 轻微变形
                        ratio_score = 0.8
                    else:  # 明显变形但可接受
                        ratio_score = 0.6
                else:
                    ratio_score = 0.2

                # 3. 像素密度评分 - 边框特征
                if 0.05 <= pixel_density <= 0.5:  # 边框密度范围
                    if 0.1 <= pixel_density <= 0.3:  # 理想边框
                        density_score = 1.0
                    else:  # 可接受范围
                        density_score = 0.7
                else:
                    density_score = 0.3

                # 4. 位置评分 - 偏向图像中心，适应小分辨率
                center_x = blob.cx()
                center_y = blob.cy()
                distance_from_center = math.sqrt((center_x - image_center_x)**2 +
                                               (center_y - image_center_y)**2)
                position_score = max(0, 1.0 - distance_from_center / 120)  # 适应320x240

                # 5. 形状规整度评分 - 检查是否像矩形
                perimeter = blob.perimeter()
                expected_perimeter = 2 * (w + h)
                if expected_perimeter > 0:
                    perimeter_ratio = perimeter / expected_perimeter
                    if 0.8 <= perimeter_ratio <= 1.3:
                        shape_score = 1.0
                    elif 0.6 <= perimeter_ratio <= 1.6:
                        shape_score = 0.7
                    else:
                        shape_score = 0.4
                else:
                    shape_score = 0.5

                # 综合评分
                total_score = (area_score * 0.25 + ratio_score * 0.25 +
                             density_score * 0.2 + position_score * 0.15 +
                             shape_score * 0.15)

                # 动态阈值 - 根据检测参数调整
                min_score = 0.5 if pixels_thresh >= 400 else 0.4

                if total_score > best_score and total_score > min_score:
                    best_score = total_score
                    best_target = blob

        # 返回最佳目标和当前阈值信息
        return best_target, current_threshold, brightness

    def calculate_target_center(blob):
        """计算目标中心，高级透视校正算法"""
        if not blob:
            return None

        # 基础几何中心
        base_center_x = blob.cx()
        base_center_y = blob.cy()

        # 获取矩形参数
        x, y, w, h = blob.x(), blob.y(), blob.w(), blob.h()
        aspect_ratio = w / h if h > 0 else 1.0
        area = w * h

        # 透视变形分析
        distortion_level = abs(aspect_ratio - 1.0)

        if distortion_level < 0.2:
            # 轻微或无变形，使用几何中心
            return (base_center_x, base_center_y)

        elif distortion_level < 0.5:
            # 中等变形，简单校正
            if aspect_ratio > 1.0:  # 水平拉伸
                correction_factor = 1.0 / math.sqrt(aspect_ratio)
                center_x = x + int(w * 0.5 * correction_factor)
                center_y = base_center_y
            else:  # 垂直拉伸
                correction_factor = math.sqrt(aspect_ratio)
                center_x = base_center_x
                center_y = y + int(h * 0.5 * correction_factor)

            return (center_x, center_y)

        else:
            # 严重变形，高级校正算法
            # 基于透视几何的校正

            # 估算透视角度
            if aspect_ratio > 1.0:  # 水平透视
                # 水平方向的透视角度估算
                tilt_angle = math.atan((aspect_ratio - 1.0) * 0.8)
                perspective_factor = 1.0 / math.cos(tilt_angle)

                # 校正中心点
                # 透视变形时，真实中心会偏向较近的一侧
                offset_x = w * 0.1 * (aspect_ratio - 1.0)
                center_x = int(base_center_x - offset_x)
                center_y = base_center_y

            else:  # 垂直透视
                # 垂直方向的透视角度估算
                inv_ratio = 1.0 / aspect_ratio
                tilt_angle = math.atan((inv_ratio - 1.0) * 0.8)
                perspective_factor = 1.0 / math.cos(tilt_angle)

                # 校正中心点
                offset_y = h * 0.1 * (inv_ratio - 1.0)
                center_x = base_center_x
                center_y = int(base_center_y - offset_y)

            # 边界检查
            center_x = max(x, min(x + w, center_x))
            center_y = max(y, min(y + h, center_y))

            return (center_x, center_y)

    def show_img_to_screen(img):
        """显示图像到屏幕 - 修复版本"""
        # K230的Image对象没有resize方法，使用scale缩放
        if img.width() < 480:
            # 计算缩放比例
            scale_x = 480 // img.width()
            scale_y = 480 // img.height()
            scale = min(scale_x, scale_y, 2)  # 最大2倍缩放
            if scale > 1:
                img = img.scale(scale, scale)

        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    # 主循环
    clock = time.clock()
    while True:
        clock.tick()
        os.exitpoint()
        current_time = ticks_ms()
        elapsed_time = (current_time - start_time) / 1000.0

        img = sensor.snapshot()

        # 如果任务已完成，显示完成状态并等待重启
        if mission_completed:
#            img.draw_string_advanced(50, 100, 40, "任务完成!", color=(0, 255, 0))
#            img.draw_string_advanced(30, 150, 30, "请重新上电复位程序", color=(255, 255, 0))
#            img.draw_string_advanced(10, 200, 20, "长按屏幕3秒进入阈值调整", color=(255, 255, 255))

            # 在完成状态下仍可进入阈值调整
            points = tp.read()
            if len(points) > 0:
                touch_counter += 1
                if touch_counter > 30:
                    threshold_adjust_mode = True
                    touch_counter = 0
                    print("进入阈值调整模式")
            else:
                touch_counter = max(0, touch_counter - 2)

            if threshold_adjust_mode:
                draw_threshold_controls(img)
                if len(points) > 0:
                    handle_threshold_touch(points[0].x, points[0].y)
                    time.sleep_ms(100)

            show_img_to_screen(img)
            continue

        # 状态机处理
        if system_state == "SEARCHING":
            # 搜索目标阶段
            result = find_target_rectangle(img)
            target_blob, current_threshold, brightness = result

            if target_blob:
                target_center = calculate_target_center(target_blob)
                if target_center:
                    system_state = "TARGET_FOUND"
                    target_found_time = current_time
                    pid_x.target = target_center[0]
                    pid_y.target = target_center[1]
                    print(f"目标发现! 中心坐标: {target_center}, 用时: {elapsed_time:.2f}秒")
                    print(f"当前阈值: {current_threshold}, 亮度: {brightness:.1f}")

                    # 绘制目标
                    img.draw_rectangle(target_blob.rect(), color=(0, 255, 0), thickness=2)
                    img.draw_cross(target_center[0], target_center[1], color=(255, 0, 0), size=10)

            # 显示搜索状态和阈值信息
            img.draw_string_advanced(10, 10, 30, f"搜索目标中... {elapsed_time:.1f}s", color=(255, 255, 0))
            img.draw_string_advanced(10, 40, 20, f"亮度: {brightness:.0f} 阈值: {current_threshold[1]}", color=(255, 255, 255))

        elif system_state == "TARGET_FOUND":
            # 目标发现，开始瞄准
            result = find_target_rectangle(img)
            target_blob, current_threshold, brightness = result

            if target_blob:
                target_center = calculate_target_center(target_blob)
                if target_center:
                    # 计算误差
                    error_x = target_center[0] - image_center_x
                    error_y = target_center[1] - image_center_y
                    distance_error = math.sqrt(error_x**2 + error_y**2)

                    # 智能PID控制步进电机
                    # 根据误差大小采用不同的控制策略

                    # X轴控制
                    if abs(error_x) > 3:  # 减小死区提高精度
                        control_x = pid_x.compute(target_center[0])

                        # 根据误差大小调整步数和速度
                        if abs(error_x) > 50:  # 大误差，快速移动
                            steps_x = int(abs(control_x) / 5)
                            speed = 3
                        elif abs(error_x) > 20:  # 中等误差，中速移动
                            steps_x = int(abs(control_x) / 8)
                            speed = 2
                        else:  # 小误差，精确移动
                            steps_x = int(abs(control_x) / 15)
                            speed = 1

                        steps_x = max(1, min(steps_x, 15))  # 限制步数范围
                        control_stepper_x(1 if error_x > 0 else -1, steps_x, speed)

                    # Y轴控制
                    if abs(error_y) > 3:  # 减小死区提高精度
                        control_y = pid_y.compute(target_center[1])

                        # 根据误差大小调整步数和速度
                        if abs(error_y) > 50:  # 大误差，快速移动
                            steps_y = int(abs(control_y) / 5)
                            speed = 3
                        elif abs(error_y) > 20:  # 中等误差，中速移动
                            steps_y = int(abs(control_y) / 8)
                            speed = 2
                        else:  # 小误差，精确移动
                            steps_y = int(abs(control_y) / 15)
                            speed = 1

                        steps_y = max(1, min(steps_y, 15))  # 限制步数范围
                        control_stepper_y(1 if error_y > 0 else -1, steps_y, speed)

                    # 绘制瞄准状态
                    img.draw_rectangle(target_blob.rect(), color=(0, 255, 0), thickness=2)
                    img.draw_cross(target_center[0], target_center[1], color=(255, 0, 0), size=10)
                    img.draw_cross(image_center_x, image_center_y, color=(0, 0, 255), size=8)
                    img.draw_line(image_center_x, image_center_y, target_center[0], target_center[1], color=(255, 255, 0))

                    # 检查是否达到瞄准精度 - 适应320x240分辨率
                    # 根据目标大小调整精度要求
                    target_area = target_blob.w() * target_blob.h()
                    if target_area > 2000:  # 近距离大目标
                        precision_threshold = 8
                    elif target_area > 1000:  # 中距离
                        precision_threshold = 10
                    else:  # 远距离小目标
                        precision_threshold = 12

                    if distance_error <= precision_threshold:
                        system_state = "AIMING"
                        print(f"瞄准完成! 误差: {distance_error:.1f}像素 (阈值: {precision_threshold})")

                    # 显示瞄准信息
                    img.draw_string_advanced(10, 10, 30, f"瞄准中... 误差: {distance_error:.1f}px", color=(255, 255, 0))
                    img.draw_string_advanced(10, 40, 20, f"X误差: {error_x:.1f}, Y误差: {error_y:.1f}", color=(255, 255, 255))

                    # 2秒超时检查
                    if elapsed_time >= 2.0:
                        system_state = "FIRING"
                        print("2秒超时，强制发射激光")
            else:
                # 目标丢失，返回搜索状态
                system_state = "SEARCHING"
                print("目标丢失，重新搜索")

        elif system_state == "AIMING":
            # 精确瞄准完成，准备发射
            result = find_target_rectangle(img)
            target_blob, current_threshold, brightness = result

            if target_blob:
                target_center = calculate_target_center(target_blob)
                if target_center:
                    # 绘制最终瞄准状态
                    img.draw_rectangle(target_blob.rect(), color=(0, 255, 0), thickness=3)
                    img.draw_cross(target_center[0], target_center[1], color=(255, 0, 0), size=12)
                    img.draw_cross(image_center_x, image_center_y, color=(0, 0, 255), size=10)

                    error_x = target_center[0] - image_center_x
                    error_y = target_center[1] - image_center_y
                    final_distance = math.sqrt(error_x**2 + error_y**2)

                    img.draw_string_advanced(10, 10, 30, "瞄准完成! 准备发射...", color=(0, 255, 0))
                    img.draw_string_advanced(10, 40, 30, f"最终误差: {final_distance:.1f}px", color=(0, 255, 0))

                    # 短暂延迟后发射
                    time.sleep_ms(200)
                    system_state = "FIRING"

        elif system_state == "FIRING":
            # 发射激光
            if laser_pin:
                laser_pin.value(1)  # 开启激光
                print("激光发射!")
            else:
                print("激光发射! (模拟模式)")

            # 显示发射状态
            img.draw_string_advanced(10, 10, 40, "激光发射中...", color=(255, 0, 0))
            img.draw_string_advanced(10, 50, 30, f"总用时: {elapsed_time:.2f}秒", color=(255, 0, 0))

            # 保持激光开启一段时间
            time.sleep_ms(1000)
            if laser_pin:
                laser_pin.value(0)  # 关闭激光

            print(f"任务完成! 总用时: {elapsed_time:.2f}秒")

            # 标记任务完成，不再重置
            mission_completed = True
            print("激光瞄准任务完成，请重新上电复位程序进行下一次瞄准")

        # 处理触摸屏输入
        points = tp.read()
        if len(points) > 0:
            touch_x, touch_y = points[0].x, points[0].y

            if threshold_adjust_mode:
                # 阈值调整模式下处理触摸
                handle_threshold_touch(touch_x, touch_y)
                time.sleep_ms(100)  # 减少延时提高响应
            else:
                # 正常模式下检测长按进入阈值调整
                touch_counter += 1
                if touch_counter > 15:  # 减少到1.5秒左右
                    threshold_adjust_mode = True
                    touch_counter = 0
                    print("进入阈值调整模式")
        else:
            touch_counter = max(0, touch_counter - 1)  # 减少衰减速度

        # 绘制阈值调整界面
        if threshold_adjust_mode:
            draw_threshold_controls(img)

        # 显示通用信息和系统状态
        status_y = 430 if not threshold_adjust_mode else 350
        img.draw_string_advanced(10, status_y, 20, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
        img.draw_string_advanced(150, status_y, 20, f"状态: {system_state}", color=(255, 255, 255))
        img.draw_string_advanced(350, status_y, 20, f"运行时间: {elapsed_time:.1f}s", color=(255, 255, 255))

        # 显示阈值模式和长按提示（仅在非调整模式下）
        if not threshold_adjust_mode:
            threshold_mode_text = "手动" if manual_threshold_mode else "自动"
            img.draw_string_advanced(10, status_y + 20, 20, f"阈值模式: {threshold_mode_text}", color=(255, 255, 0))
            if manual_threshold_mode:
                img.draw_string_advanced(150, status_y + 20, 20, f"阈值: {manual_threshold[1]}", color=(255, 255, 0))

            # 显示长按提示和进度
            if touch_counter > 0:
                progress = min(100, int(touch_counter * 100 / 15))
                img.draw_string_advanced(10, status_y + 40, 16, f"长按进度: {progress}%", color=(0, 255, 255))
            else:
                img.draw_string_advanced(10, status_y + 40, 16, "长按屏幕1.5秒进入阈值调整", color=(128, 128, 128))

        # 显示十字准星 - 图像中心
        img.draw_cross(image_center_x, image_center_y, color=(128, 128, 128), size=5)

        # 显示图像
        show_img_to_screen(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 确保激光关闭
    if 'laser_pin' in locals() and laser_pin:
        laser_pin.value(0)

    # 停止所有GPIO
    if 'step_x1' in locals() and step_x1:
        step_x1.value(0)
        step_x2.value(0)
    if 'step_y1' in locals() and step_y1:
        step_y1.value(0)
        step_y2.value(0)

    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("激光瞄准系统已安全关闭")

